<template>
  <div class="district-building-stats">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="区内楼宇信息统计" />
        <div class="stats-grid">
          <div class="stat-card" v-for="stat in buildingStats" :key="stat.label">
            <div class="card-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-unit">{{ stat.unit }}</div>
            </div>
            <div class="wave-effect"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 接收父组件传递的选中区域信息和统计数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  },
  summaryData: {
    type: Object,
    default: () => ({
      buildingNums: 0,
      investedArea: 0,
      vacantArea: 0,
      rentAmtAvg: 0
    })
  }
});

// 动态楼宇统计数据
const buildingStats = computed(() => {
  return [
    {
      label: '楼宇数量',
      value: props.summaryData.buildingNums || 0,
      unit: '栋'
    },
    {
      label: '招商面积',
      value: (props.summaryData.investedArea / 10000).toFixed(1) || '0.0',
      unit: '万㎡'
    },
    {
      label: '空置面积',
      value: (props.summaryData.vacantArea / 10000).toFixed(1) || '0.0',
      unit: '万㎡'
    },
    {
      label: '平均租金',
      value: props.summaryData.rentAmtAvg?.toFixed(1) || '0.0',
      unit: '元/㎡·月'
    }
  ];
});
</script>

<style scoped lang="less">
.district-building-stats {
  height: 100%;
}

.border-box {
  height: 100%;
  background-size: 100% 100%;
  padding: 20px; /* 减少内边距 */
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
/* .title-bg 样式已移至ChartTitle组件 */

/* 原有的标题样式已移至ChartTitle组件 */

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px; /* 进一步减少卡片间距 */
  align-items: center;
  margin-top: 15px;
}

.stat-card {
  position: relative;
  padding: 8px; /* 减少内边距 */
  background: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(129, 212, 250, 0.05) 100%);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 70px; /* 减少最小高度 */
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
  border-color: rgba(79, 195, 247, 0.6);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.3;
}

.bt-icon-bg {
  width: 60%;
  height: 60%;
  object-fit: contain;
}

.card-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.stat-value {
  font-size: 22px; /* 减少字体大小 */
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 12px rgba(79, 195, 247, 0.6);
  font-family: 'Courier New', monospace;
  margin-bottom: 6px;
  animation: valueGlow 2s ease-in-out infinite alternate;
}

@keyframes valueGlow {
  0% {
    text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  }
  100% {
    text-shadow: 0 0 25px rgba(79, 195, 247, 0.9), 0 0 35px rgba(129, 212, 250, 0.6);
  }
}

.stat-label {
  color: #e3f2fd;
  font-size: 11px; /* 减少字体大小 */
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 3px;
}

.stat-unit {
  color: #4fc3f7;
  font-size: 9px; /* 减少字体大小 */
  opacity: 0.8;
}

/* 波光特效 */
.wave-effect {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(79, 195, 247, 0.2),
    rgba(129, 212, 250, 0.4),
    rgba(79, 195, 247, 0.2),
    transparent
  );
  animation: wave 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes wave {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

.stat-card:hover .wave-effect {
  animation-duration: 1.5s;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .stats-grid {
    gap: 15px;
  }

  .stat-card {
    padding: 12px;
    min-height: 70px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 11px;
  }
}

@media (max-width: 1200px) {
  .stats-grid {
    gap: 12px;
  }

  .stat-card {
    padding: 10px;
    min-height: 60px;
  }

  .stat-value {
    font-size: 18px;
  }

  .stat-label {
    font-size: 10px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-card {
    padding: 12px;
    min-height: 80px;
  }

  .stat-value {
    font-size: 18px;
  }
}
</style>
