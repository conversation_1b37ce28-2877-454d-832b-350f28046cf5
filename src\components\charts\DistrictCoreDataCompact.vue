<template>
  <div class="district-core-data-compact">
    <div class="stats-grid">
      <div class="stat-item" v-for="stat in statsData" :key="stat.label">
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-icon">
          <img src="@/assets/icon/dizuo.svg" class="icon-bg" alt="data icon">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '芙蓉区',
      level: 'district',
      areaData: null
    })
  }
});

// 区级核心数据配置
const districtCoreDataConfig = {
  '芙蓉区': {
    buildingEconomyOutput: '286.5',
    gdpTotal: '1,245.8',
    buildingCount: '168',
    companyCount: '2,845'
  },
  '天心区': {
    buildingEconomyOutput: '198.3',
    gdpTotal: '987.2',
    buildingCount: '124',
    companyCount: '1,967'
  },
  '岳麓区': {
    buildingEconomyOutput: '342.1',
    gdpTotal: '1,567.9',
    buildingCount: '203',
    companyCount: '3,421'
  },
  '开福区': {
    buildingEconomyOutput: '234.7',
    gdpTotal: '1,123.4',
    buildingCount: '156',
    companyCount: '2,234'
  },
  '雨花区': {
    buildingEconomyOutput: '267.8',
    gdpTotal: '1,334.6',
    buildingCount: '189',
    companyCount: '2,678'
  },
  '望城区': {
    buildingEconomyOutput: '145.2',
    gdpTotal: '678.9',
    buildingCount: '98',
    companyCount: '1,456'
  }
};

// 计算当前区域的核心数据
const coreStats = computed(() => {
  const regionName = props.selectedRegion.regionName;
  return districtCoreDataConfig[regionName] || districtCoreDataConfig['芙蓉区'];
});

// 统计数据（与首页格式保持一致）
const statsData = computed(() => [
  { label: '楼宇经济年度产值', value: coreStats.value.buildingEconomyOutput + '亿元' },
  { label: 'GDP总额', value: coreStats.value.gdpTotal + '亿元' },
  { label: '楼宇总数', value: coreStats.value.buildingCount + '栋' },
  { label: '入驻企业总数', value: coreStats.value.companyCount + '家' }
]);
</script>

<style scoped lang="less">
.district-core-data-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.stats-grid {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 120px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  margin-bottom: 8px;
  z-index: 2;
  position: relative;
  animation: valueGlow 2s ease-in-out infinite alternate;
}

@keyframes valueGlow {
  0% {
    text-shadow: 0 0 15px rgba(79, 195, 247, 0.6);
  }
  100% {
    text-shadow: 0 0 25px rgba(79, 195, 247, 0.9);
  }
}

.stat-label {
  color: #e3f2fd;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
  z-index: 2;
  position: relative;
  text-align: center;
}

.stat-icon {
  position: absolute;
  bottom: -70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.icon-bg {
  width: 120px;
  height: 120px;
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}


</style>
