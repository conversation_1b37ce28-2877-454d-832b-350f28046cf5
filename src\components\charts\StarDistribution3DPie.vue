<template>
  <div class="star-distribution-3d-pie">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="星级分布" />
        <div ref="starPieChartRef" class="echarts-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import * as echarts from 'echarts';
import 'echarts-gl';
import { useColors } from '@/composables/useColors';
import ChartTitle from '@/components/common/ChartTitle.vue';
const { colors, currentTheme, toggleTheme, getCSSVars } = useColors()

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  }
});

// 3D饼图相关变量
const starPieChartRef = ref(null);
let starPieChart = null;

// 区域星级分布数据配置
const districtStarData = {
  '芙蓉区': [
    { name: '4星级', value: 25, itemStyle: { color: '#ff7043' ,opacity:0.7 } },
    { name: '5星级', value: 45, itemStyle: { color: '#ffa726' ,opacity:0.7 } },
    { name: '6星级', value: 38, itemStyle: { color: '#66bb6a' ,opacity:0.7 } },
    { name: '7星级', value: 60, itemStyle: { color: '#4fc3f7' ,opacity:0.7 } }
  ],
  '天心区': [
    { name: '4星级', value: 18, itemStyle: { color: '#ff7043' ,opacity:0.7 } },
    { name: '5星级', value: 32, itemStyle: { color: '#ffa726',opacity:0.7  } },
    { name: '6星级', value: 28, itemStyle: { color: '#66bb6a',opacity:0.7  } },
    { name: '7星级', value: 46, itemStyle: { color: '#4fc3f7' ,opacity:0.7 } }
  ],
  '岳麓区': [
    { name: '4星级', value: 35, itemStyle: { color: '#ff7043' ,opacity:0.7 } },
    { name: '5星级', value: 58, itemStyle: { color: '#ffa726' ,opacity:0.7 } },
    { name: '6星级', value: 52, itemStyle: { color: '#66bb6a',opacity:0.7  } },
    { name: '7星级', value: 58, itemStyle: { color: '#4fc3f7',opacity:0.7  } }
  ],
  '开福区': [
    { name: '4星级', value: 22, itemStyle: { color: '#ff7043',opacity:0.7  } },
    { name: '5星级', value: 38, itemStyle: { color: '#ffa726' ,opacity:0.7 } },
    { name: '6星级', value: 34, itemStyle: { color: '#66bb6a',opacity:0.7  } },
    { name: '7星级', value: 62, itemStyle: { color: '#4fc3f7',opacity:0.7  } }
  ],
  '雨花区': [
    { name: '4星级', value: 28, itemStyle: { color: '#ff7043' ,opacity:0.7 } },
    { name: '5星级', value: 48, itemStyle: { color: '#ffa726' ,opacity:0.7 } },
    { name: '6星级', value: 42, itemStyle: { color: '#66bb6a',opacity:0.7  } },
    { name: '7星级', value: 71, itemStyle: { color: '#4fc3f7' ,opacity:0.7 } }
  ],
  '望城区': [
    { name: '4星级', value: 15, itemStyle: { color: '#ff7043',opacity:0.7  } },
    { name: '5星级', value: 25, itemStyle: { color: '#ffa726',opacity:0.7  } },
    { name: '6星级', value: 22, itemStyle: { color: '#66bb6a',opacity:0.7  } },
    { name: '7星级', value: 36, itemStyle: { color: '#4fc3f7',opacity:0.7  } }
  ],
  '长沙县': [
    { name: '4星级', value: 42, itemStyle: { color: '#ff7043',opacity:0.7  } },
    { name: '5星级', value: 68, itemStyle: { color: '#ffa726',opacity:0.7  } },
    { name: '6星级', value: 58, itemStyle: { color: '#66bb6a',opacity:0.7  } },
    { name: '7星级', value: 66, itemStyle: { color: '#4fc3f7',opacity:0.7  } }
  ]
};

// 默认全市数据
const cityStarData = [
  { name: '4星级', value: 155, itemStyle: { color: colors.pie[400],opacity:0.7 }},
  { name: '5星级', value: 103, itemStyle: { color: colors.pie[800],opacity:0.7 }},
  { name: '6星级', value: 55, itemStyle: { color: colors.pie[1200],opacity:0.7 } },
  { name: '7星级', value: 81, itemStyle: { color: colors.pie[1600],opacity:0.7 }}
];

// 动态星级分布数据
const starDistributionData = computed(() => {
  const regionName = props.selectedRegion.regionName;
  if (props.selectedRegion.level === 'district' && districtStarData[regionName]) {
    return districtStarData[regionName];
  }
  return cityStarData;
});

/**
 * 获取面的参数方程
 * @param {*} startRatio 扇形起始位置比例
 * @param {*} endRatio 扇形结束位置比例
 * @param {*} k 辅助参数,控制饼图半径
 * @param {*} value 数值
 */
const getParametricEquation = (startRatio, endRatio, k, value) => {
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;

  k = typeof k === "number" && !isNaN(k) ? k : 1 / 3; //默认1/3

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x(u, v) {
      if (u < startRadian) {
        return Math.cos(startRadian) * (1 + Math.cos(v) * k);
      }
      if (u > endRadian) {
        return Math.cos(endRadian) * (1 + Math.cos(v) * k);
      }
      return Math.cos(u) * (1 + Math.cos(v) * k);
    },

    y(u, v) {
      if (u < startRadian) {
        return Math.sin(startRadian) * (1 + Math.cos(v) * k);
      }
      if (u > endRadian) {
        return Math.sin(endRadian) * (1 + Math.cos(v) * k);
      }
      return Math.sin(u) * (1 + Math.cos(v) * k);
    },

    z(u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * value * 0.02; // 降低高度系数
      }
      // 扇形高度根据value值计算，降低高度让3D效果更平缓
      return Math.sin(v) > 0 ? value * 0.02 : -1;
    },
  };
};

// 初始化星级分布饼图（3D效果）
const initStarPieChart = () => {
  if (!starPieChartRef.value) return;

  starPieChart = echarts.init(starPieChartRef.value);

  // 使用星级分布数据生成3D饼图配置
  const starData = starDistributionData.value;

  //总数
  let total = starData.reduce((a, b) => a + b.value, 0);
  //当前累加值
  let sumValue = 0;
  //辅助参数,控制饼图半径，（0-1）范围内控制环形大小，值越小环形内半径越大
  let k = 1;

  //series配置（每个扇形）
  let series = starData.map((item) => {
    //当前扇形起始位置占饼图比例
    let startRatio = sumValue / total;
    //值累加
    sumValue += item.value;
    //当前扇形结束位置占饼图比例
    let endRatio = sumValue / total;

    return {
      name: item.name ?? null,
      type: "surface", //曲面图
      itemStyle: {
        color: item.itemStyle.color ?? null, //颜色
        opacity:item.itemStyle.opacity ?? 1,
      },

      wireframe: {
        show: false, //不显示网格线
      },
      pieData: item, //数据
      //饼图状态
      pieStatus: {
        k, //辅助参数
        startRatio, //起始位置比例
        endRatio, //结束位置比例
        value: item.value, //数值
      },
      parametric: true, //参数曲面
      //曲面的参数方程
      parametricEquation: getParametricEquation(
        startRatio,
        endRatio,
        k,
        item.value
      ),
    };
  });

  const option = {
    backgroundColor: 'transparent',
    //提示框
    tooltip: {
      formatter: (params) => {
        if (
          params.seriesName !== "mouseoutSeries" &&
          params.seriesName !== "pie2d"
        ) {
          return `${
            params.seriesName
          }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
            params.color
          };"></span>${series[params.seriesIndex].pieData.value}`;
        }
        return "";
      },
    },
    // 添加底部图例
    legend: {
      orient: 'horizontal',
      bottom: '-2%',
      left: 'center',
      textStyle: {
        color: '#e3f2fd',
        fontSize: 12
      },
      itemWidth: 10,
      itemHeight: 8,
      itemGap: 15,
      data: starData.map(item => ({
        name: item.name,
        icon: 'circle'
      }))
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    //
    grid3D: {
      show: false, //不显示坐标系
      boxHeight: 15, //饼图高度，降低高度让3D效果更平缓
      top: '-14%', // 上移图表位置，匹配背景3D图标
      bottom: '30%', // 给底部图例留出更多空间，增加20px间隔
      // 用于鼠标的旋转，缩放等视角控制
      viewControl: {
        alpha: 20, //视角，降低10度让饼图更扁平
        distance: 370, //距离，进一步拉近让饼图更大
        rotateSensitivity: 1, //启用旋转，默认值为1
        zoomSensitivity: 1, //启用缩放，默认值为1
        panSensitivity: 1, //启用平移，默认值为1
        autoRotate: true, //禁止自动旋转
        // 可以设置旋转和缩放的限制
        minDistance: 80, //最小缩放距离，相应调整
        maxDistance: 500, //最大缩放距离，相应调整
        minAlpha: -90, //最小垂直旋转角度
        maxAlpha: 90, //最大垂直旋转角度
      },
    },
    series,
  };

  starPieChart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (starPieChart) starPieChart.resize();
};

// 监听选中区域变化
watch(() => props.selectedRegion, () => {
  if (starPieChart) {
    initStarPieChart();
  }
}, { deep: true });

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initStarPieChart();
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (starPieChart) {
    starPieChart.dispose();
  }
});
</script>

<style scoped lang="less">
.star-distribution-3d-pie {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 10px 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}
.title-bg{
  position: relative;
  z-index: 1;
  left: -10;
  top: 0;
  width: 100%;
  // width: min(280px, 70%); /* 响应式宽度：最大280px，最小70%容器宽度 */
  height: 40px; /* 固定高度，与容器一致 */
  // object-fit: contain;
}
.section-title {
  position: absolute;
  z-index: 2;
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
  left: 40px;
  text-align: left;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  white-space: nowrap;
  margin: 0;
  padding: 0;
  line-height: 40px;
}
.border-box::before {
  content: '';
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('@/assets/icon/dataicon.svg');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 55%; /* 进一步缩小背景图到50% */
  // opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.echarts-container {
  flex: 1;
  width: 100%;
  min-height: 180px; /* 减少最小高度，适应紧凑布局 */
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .title-wrapper {
    margin-bottom: 8px;
    height: 35px;
  }

  .section-title {
    font-size: 13px;
  }

  .echarts-container {
    min-height: 160px;
  }
}

@media (max-width: 1200px) {
  .title-wrapper {
    margin-bottom: 6px;
    height: 30px;
  }

  .section-title {
    font-size: 12px;
  }

  .echarts-container {
    min-height: 150px;
  }
}
</style>
