<template>
  <div class="industry-distribution-bar">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="行业分布" />
        <div
          ref="industryBarChartRef"
          class="echarts-container"
          @mouseenter="stopAutoScroll"
          @mouseleave="startAutoScroll"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from "vue";
import * as echarts from "echarts";
import ChartTitle from '@/components/common/ChartTitle.vue';

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: "长沙市",
      level: "city",
      areaData: null
    })
  }
});

// 横向柱状图相关变量
const industryBarChartRef = ref(null);
let industryBarChart = null;

// 自动滚动相关变量
const currentStartIndex = ref(0);
const displayCount = 8; // 一次显示8条数据
let scrollTimer = null;

// 区域行业分布数据配置（扩展到15条）
const districtIndustryData = {
  芙蓉区: [
    { name: "金融服务", value: 285, itemStyle: { color: "#4fc3f7", opacity: 0.7 } },
    { name: "商贸物流", value: 198, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "文化旅游", value: 156, itemStyle: { color: "#26c6da", opacity: 0.7 } },
    { name: "科技创新", value: 142, itemStyle: { color: "#ff7043", opacity: 0.7 } },
    { name: "房地产业", value: 128, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "教育培训", value: 115, itemStyle: { color: "#ef5350", opacity: 0.7 } },
    { name: "医疗健康", value: 102, itemStyle: { color: "#42a5f5", opacity: 0.7 } },
    { name: "餐饮服务", value: 95, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "零售批发", value: 89, itemStyle: { color: "#ffa726", opacity: 0.7 } },
    { name: "建筑工程", value: 78, itemStyle: { color: "#8bc34a", opacity: 0.7 } },
    { name: "交通运输", value: 72, itemStyle: { color: "#ff9800", opacity: 0.7 } },
    { name: "信息技术", value: 65, itemStyle: { color: "#9c27b0", opacity: 0.7 } },
    { name: "法律咨询", value: 58, itemStyle: { color: "#607d8b", opacity: 0.7 } },
    { name: "广告传媒", value: 52, itemStyle: { color: "#795548", opacity: 0.7 } },
    { name: "其他服务", value: 45, itemStyle: { color: "#9e9e9e", opacity: 0.7 } }
  ],
  天心区: [
    { name: "文化旅游", value: 198, itemStyle: { color: "#26c6da", opacity: 0.7 } },
    { name: "金融服务", value: 165, itemStyle: { color: "#4fc3f7", opacity: 0.7 } },
    { name: "商贸物流", value: 134, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "科技创新", value: 98, itemStyle: { color: "#ff7043", opacity: 0.7 } },
    { name: "餐饮服务", value: 89, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "零售批发", value: 82, itemStyle: { color: "#ffa726", opacity: 0.7 } },
    { name: "房地产业", value: 75, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "教育培训", value: 68, itemStyle: { color: "#ef5350", opacity: 0.7 } },
    { name: "医疗健康", value: 62, itemStyle: { color: "#42a5f5", opacity: 0.7 } },
    { name: "建筑工程", value: 58, itemStyle: { color: "#8bc34a", opacity: 0.7 } },
    { name: "交通运输", value: 52, itemStyle: { color: "#ff9800", opacity: 0.7 } },
    { name: "信息技术", value: 48, itemStyle: { color: "#9c27b0", opacity: 0.7 } },
    { name: "法律咨询", value: 42, itemStyle: { color: "#607d8b", opacity: 0.7 } },
    { name: "广告传媒", value: 38, itemStyle: { color: "#795548", opacity: 0.7 } },
    { name: "其他服务", value: 32, itemStyle: { color: "#9e9e9e", opacity: 0.7 } }
  ],
  岳麓区: [
    { name: "科技创新", value: 342, itemStyle: { color: "#ff7043", opacity: 0.7 } },
    { name: "制造业", value: 256, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "金融服务", value: 189, itemStyle: { color: "#4fc3f7", opacity: 0.7 } },
    { name: "商贸物流", value: 145, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "教育培训", value: 132, itemStyle: { color: "#ef5350", opacity: 0.7 } },
    { name: "信息技术", value: 125, itemStyle: { color: "#9c27b0", opacity: 0.7 } },
    { name: "房地产业", value: 118, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "医疗健康", value: 108, itemStyle: { color: "#42a5f5", opacity: 0.7 } },
    { name: "文化旅游", value: 98, itemStyle: { color: "#26c6da", opacity: 0.7 } },
    { name: "建筑工程", value: 89, itemStyle: { color: "#8bc34a", opacity: 0.7 } },
    { name: "餐饮服务", value: 82, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "零售批发", value: 75, itemStyle: { color: "#ffa726", opacity: 0.7 } },
    { name: "交通运输", value: 68, itemStyle: { color: "#ff9800", opacity: 0.7 } },
    { name: "法律咨询", value: 62, itemStyle: { color: "#607d8b", opacity: 0.7 } },
    { name: "其他服务", value: 55, itemStyle: { color: "#9e9e9e", opacity: 0.7 } }
  ],
  开福区: [
    { name: "商贸物流", value: 234, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "制造业", value: 198, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "金融服务", value: 156, itemStyle: { color: "#4fc3f7", opacity: 0.7 } },
    { name: "文化旅游", value: 123, itemStyle: { color: "#26c6da", opacity: 0.7 } },
    { name: "房地产业", value: 112, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "交通运输", value: 105, itemStyle: { color: "#ff9800", opacity: 0.7 } },
    { name: "餐饮服务", value: 98, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "零售批发", value: 89, itemStyle: { color: "#ffa726", opacity: 0.7 } },
    { name: "建筑工程", value: 82, itemStyle: { color: "#8bc34a", opacity: 0.7 } },
    { name: "教育培训", value: 75, itemStyle: { color: "#ef5350", opacity: 0.7 } },
    { name: "医疗健康", value: 68, itemStyle: { color: "#42a5f5", opacity: 0.7 } },
    { name: "信息技术", value: 62, itemStyle: { color: "#9c27b0", opacity: 0.7 } },
    { name: "法律咨询", value: 55, itemStyle: { color: "#607d8b", opacity: 0.7 } },
    { name: "广告传媒", value: 48, itemStyle: { color: "#795548", opacity: 0.7 } },
    { name: "其他服务", value: 42, itemStyle: { color: "#9e9e9e", opacity: 0.7 } }
  ],
  雨花区: [
    { name: "制造业", value: 298, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "科技创新", value: 234, itemStyle: { color: "#ff7043", opacity: 0.7 } },
    { name: "商贸物流", value: 189, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "金融服务", value: 145, itemStyle: { color: "#4fc3f7", opacity: 0.7 } },
    { name: "信息技术", value: 132, itemStyle: { color: "#9c27b0", opacity: 0.7 } },
    { name: "房地产业", value: 125, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
    { name: "建筑工程", value: 118, itemStyle: { color: "#8bc34a", opacity: 0.7 } },
    { name: "交通运输", value: 108, itemStyle: { color: "#ff9800", opacity: 0.7 } },
    { name: "教育培训", value: 98, itemStyle: { color: "#ef5350", opacity: 0.7 } },
    { name: "医疗健康", value: 89, itemStyle: { color: "#42a5f5", opacity: 0.7 } },
    { name: "餐饮服务", value: 82, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
    { name: "零售批发", value: 78, itemStyle: { color: "#ffa726", opacity: 0.7 } },
    { name: "文化旅游", value: 72, itemStyle: { color: "#26c6da", opacity: 0.7 } },
    { name: "法律咨询", value: 65, itemStyle: { color: "#607d8b", opacity: 0.7 } },
    { name: "其他服务", value: 58, itemStyle: { color: "#9e9e9e", opacity: 0.7 } }
  ],
  望城区: [
    {
      name: "制造业",
      value: 156,
      itemStyle: { color: "#66bb6a", opacity: 0.7 }
    },
    {
      name: "商贸物流",
      value: 123,
      itemStyle: { color: "#ab47bc", opacity: 0.7 }
    },
    {
      name: "科技创新",
      value: 89,
      itemStyle: { color: "#ff7043", opacity: 0.7 }
    },
    {
      name: "金融服务",
      value: 67,
      itemStyle: { color: "#4fc3f7", opacity: 0.7 }
    },
    { name: "其他", value: 45, itemStyle: { color: "#ffa726", opacity: 0.7 } }
  ],
  长沙县: [
    {
      name: "制造业",
      value: 398,
      itemStyle: { color: "#66bb6a", opacity: 0.7 }
    },
    {
      name: "科技创新",
      value: 298,
      itemStyle: { color: "#ff7043", opacity: 0.7 }
    },
    {
      name: "商贸物流",
      value: 234,
      itemStyle: { color: "#ab47bc", opacity: 0.7 }
    },
    {
      name: "金融服务",
      value: 156,
      itemStyle: { color: "#4fc3f7", opacity: 0.7 }
    },
    { name: "其他", value: 98, itemStyle: { color: "#ffa726", opacity: 0.7 } }
  ]
};

// 默认全市数据（扩展到15条）
const cityIndustryData = [
  { name: "金融服务", value: 1048, itemStyle: { color: "#4fc3f7", opacity: 0.7 } },
  { name: "制造业", value: 735, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
  { name: "科技创新", value: 580, itemStyle: { color: "#ff7043", opacity: 0.7 } },
  { name: "商贸物流", value: 484, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
  { name: "房地产业", value: 438, itemStyle: { color: "#66bb6a", opacity: 0.7 } },
  { name: "教育培训", value: 388, itemStyle: { color: "#ef5350", opacity: 0.7 } },
  { name: "信息技术", value: 342, itemStyle: { color: "#9c27b0", opacity: 0.7 } },
  { name: "文化旅游", value: 300, itemStyle: { color: "#26c6da", opacity: 0.7 } },
  { name: "建筑工程", value: 268, itemStyle: { color: "#8bc34a", opacity: 0.7 } },
  { name: "医疗健康", value: 235, itemStyle: { color: "#42a5f5", opacity: 0.7 } },
  { name: "交通运输", value: 205, itemStyle: { color: "#ff9800", opacity: 0.7 } },
  { name: "餐饮服务", value: 178, itemStyle: { color: "#ab47bc", opacity: 0.7 } },
  { name: "零售批发", value: 152, itemStyle: { color: "#ffa726", opacity: 0.7 } },
  { name: "法律咨询", value: 128, itemStyle: { color: "#607d8b", opacity: 0.7 } },
  { name: "其他服务", value: 98, itemStyle: { color: "#9e9e9e", opacity: 0.7 } }
];

// 动态行业分布数据（完整数据）
const industryDistributionData = computed(() => {
  const regionName = props.selectedRegion.regionName;
  if (
    props.selectedRegion.level === "district" &&
    districtIndustryData[regionName]
  ) {
    return districtIndustryData[regionName];
  }
  return cityIndustryData;
});

// 当前显示的数据（滚动切片）
const currentDisplayData = computed(() => {
  const fullData = industryDistributionData.value;
  const startIndex = currentStartIndex.value;
  const endIndex = startIndex + displayCount;

  // 如果数据不足displayCount条，从头开始补充
  if (endIndex <= fullData.length) {
    return fullData.slice(startIndex, endIndex);
  } else {
    // 数据不足时，从头开始循环
    const firstPart = fullData.slice(startIndex);
    const secondPart = fullData.slice(0, endIndex - fullData.length);
    return [...firstPart, ...secondPart];
  }
});



// 初始化行业分布横向柱状图
const initIndustryBarChart = () => {
  if (!industryBarChartRef.value) return;

  industryBarChart = echarts.init(industryBarChartRef.value);

  // 使用当前显示的数据生成横向柱状图配置
  const industryData = currentDisplayData.value;

  // 提取行业名称和数值
  const categories = industryData.map(item => item.name);
  const values = industryData.map(item => item.value);
  const colors = industryData.map(item => item.itemStyle.color);

  const option = {
    backgroundColor: "transparent",
    grid: {
      top: '15%',
      left: '15%',
      right: '8%',
      bottom: '15%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 20, 50, 0.9)',
      borderColor: '#4fc3f7',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: function(params) {
        const param = params[0];
        return `<div style="font-weight: bold; margin-bottom: 5px;">${param.name}</div>
                <div style="margin: 2px 0;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; margin-right: 5px;"></span>
                  企业数量: ${param.value}家
                </div>`;
      }
    },
    xAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#4fc3f7',
          width: 1
        }
      },
      axisLabel: {
        color: '#4fc3f7',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.2)',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: {
          color: '#4fc3f7',
          width: 1
        }
      },
      axisLabel: {
        color: '#4fc3f7',
        fontSize: 11
      },
      axisTick: {
        lineStyle: {
          color: '#4fc3f7'
        }
      }
    },
    series: [{
      type: 'bar',
      data: values.map((value, index) => ({
        value: value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: colors[index] },
            { offset: 0.5, color: colors[index] + 'CC' },
            { offset: 1, color: colors[index] + '88' }
          ]),
          borderRadius: [0, 8, 8, 0] // 右侧圆角，类似子弹头
        }
      })),
      barHeight: '60%', // 控制柱子高度，类似细长条形
      emphasis: {
        itemStyle: {
          shadowBlur: 15,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animationDelay: function (idx) {
        return idx * 100;
      }
    }],
    animationEasing: 'cubicOut',
    animationDuration: 1000
  };

  industryBarChart.setOption(option);
};

// 自动滚动函数
const startAutoScroll = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer);
  }

  scrollTimer = setInterval(() => {
    const fullData = industryDistributionData.value;
    currentStartIndex.value = (currentStartIndex.value + 1) % fullData.length;

    // 更新图表
    if (industryBarChart) {
      initIndustryBarChart();
    }
  }, 3000); // 每3秒滚动一次
};

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer);
    scrollTimer = null;
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (industryBarChart) industryBarChart.resize();
};

// 监听选中区域变化
watch(
  () => props.selectedRegion,
  () => {
    if (industryBarChart) {
      // 重置滚动索引
      currentStartIndex.value = 0;
      // 重新初始化图表
      initIndustryBarChart();
      // 重新启动自动滚动
      startAutoScroll();
    }
  },
  { deep: true }
);

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initIndustryBarChart();
    // 启动自动滚动
    startAutoScroll();
  });

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  // 停止自动滚动
  stopAutoScroll();

  window.removeEventListener("resize", handleResize);
  if (industryBarChart) {
    industryBarChart.dispose();
  }
});
</script>

<style scoped lang="less">
.industry-distribution-bar {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 10px 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}
.title-bg{
  position: relative;
  z-index: 1;
  left: -10;
  top: 0;
  width: 100%;
  // width: min(280px, 70%); /* 响应式宽度：最大280px，最小70%容器宽度 */
  height: 40px; /* 固定高度，与容器一致 */
  // object-fit: contain;
}
.section-title {
  position: absolute;
  z-index: 2;
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
  left: 40px;
  text-align: left;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  white-space: nowrap;
  margin: 0;
  padding: 0;
  line-height: 40px;
}
.border-box::before {
  content: "";
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  // opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}
.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.echarts-container {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
